package com.zhentao.kafka;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import java.util.Properties;
import java.util.concurrent.Future;

public class SimpleKafkaProducer {
    private KafkaProducer<String, String> producer;
    
    public SimpleKafkaProducer() {
        Properties props = new Properties();
        props.put("bootstrap.servers", "localhost:9092");
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        this.producer = new KafkaProducer<>(props);
    }
    
    public void sendWithRetry(String topic, String key, String value) throws Exception {
        KafkaRetryUtil.retryKafkaOperation(() -> {
            Future<RecordMetadata> future = producer.send(new ProducerRecord<>(topic, key, value));
            try {
                return future.get(); // 等待发送完成
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, 3);
    }
}