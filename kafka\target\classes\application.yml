server:
  port: 1000
spring:
  rabbitmq:
    host: ***************
    port: 5672
    username: myuser
    password: mypassword
  kafka:
    bootstrap-servers: ***************:9092
    producer:
      retries: 10
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1  #生产者发送消息确认机制
    consumer:
      group-id: ${spring.application.name}-test
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer