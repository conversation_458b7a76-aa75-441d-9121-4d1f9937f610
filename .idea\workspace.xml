<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="40e85953-ba9f-4e9d-b8bb-837c2a6bedb3" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="$PROJECT_DIR$/../../../yjssi/apache-maven-3.9.9" />
        <option name="userSettingsFile" value="D:\yjssi\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="workspaceImportEnabled" value="true" />
      </MavenImportingSettings>
    </option>
    <option name="disabledProfiles">
      <list>
        <option value="jdk17" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="30aVKnq2dApMwiVp6GpQEmsG7Cb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "jdk.selected.JAVA_MODULE": "1.8",
    "last_opened_file_path": "D:/zhuangaoliu-javafile/day7-30/untitled/kafka/src/main/java/com/zhentao",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "settings.editor.selected.configurable": "vcs.Git",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\zhuangaoliu-javafile\day7-30\untitled\kafka\src\main\java\com\zhentao" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\zhuangaoliu-javafile\day7-30\untitled\kafka\src\main\java\com\zhentao\controller" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="kafka" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.Application" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="40e85953-ba9f-4e9d-b8bb-837c2a6bedb3" name="更改" comment="" />
      <created>1753865609671</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753865609671</updated>
      <workItem from="1753865611021" duration="5487000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>