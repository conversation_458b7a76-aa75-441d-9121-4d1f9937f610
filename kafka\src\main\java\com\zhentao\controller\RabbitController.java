package com.zhentao.controller;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@RestController
@RequestMapping("/rabbit")
public class RabbitController {
    @Autowired
    RabbitTemplate rabbitTemplate;


    @RequestMapping("/send")
    public void send(){
        rabbitTemplate.convertAndSend("simple.queue", "hello world");
    }
    @RequestMapping("get")
    public void get(){
        rabbitTemplate.receiveAndConvert("simple.queue");
    }
    @RequestMapping("/chongShi")
    public String chongShi(@RequestParam(defaultValue = "0") Integer retryCount) {
        try {
            // 模拟优惠券发放逻辑
            if (Math.random() < 0.7) { // 70%概率失败
                throw new RuntimeException("Coupon发放失败");
            }
            System.out.println("优惠券发放成功");
            return "发放成功";
        } catch (Exception e) {
            if (retryCount >= 2) {
                System.out.println("达到最大重试次数，放入死信队列");
                // 发送到死信队列或记录日志
                return "发放失败，已达最大重试次数";
            }

            rabbitTemplate.convertAndSend("simple.queue", "retry-" + (retryCount + 1));
            System.out.println("消息已重新入队等待重试，当前重试次数: " + (retryCount + 1));
            return "重试中...";
        }
    }

}
