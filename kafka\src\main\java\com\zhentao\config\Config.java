package com.zhentao.config;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.web.client.RestTemplate;

public class Config {
    RestTemplate restTemplate;
    @RabbitListener(queues = "simple.queue")
    public void handleRetry(String message) {
        // 从消息体中解析重试次数
        if (message.startsWith("retry-")) {
            String retryCountStr = message.substring(6); // 去掉"retry-"前缀
            Integer retryCount = Integer.parseInt(retryCountStr);
            System.out.println("接收到重试消息，重试次数: " + retryCount);
            // 调用重试接口
            restTemplate.getForObject("http://localhost:1000/chongShi?retryCount=" + retryCount, String.class);
        }
    }
}
