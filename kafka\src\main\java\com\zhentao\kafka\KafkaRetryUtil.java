package com.zhentao.kafka;

import org.apache.kafka.common.errors.RetriableException;
import java.util.function.Supplier;

public class KafkaRetryUtil {
    
    public static <T> T retryKafkaOperation(Supplier<T> operation, int maxRetries) throws Exception {
        Exception lastException = null;
        
        for (int i = 0; i <= maxRetries; i++) {
            try {
                return operation.get();
            } catch (RetriableException e) {
                lastException = e;
                if (i < maxRetries) {
                    Thread.sleep(1000 * (i + 1)); // 递增等待时间
                }
            } catch (Exception e) {
                // 非可重试异常直接抛出
                throw e;
            }
        }
        throw lastException;
    }
}